import { useState, useEffect, useCallback, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { BiddingPanel } from "@/components/BiddingPanel";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { liveReels, bidHistories } from "@/lib/data";
import { LiveReel, Bid, SocketNewBidData, SocketAuctionUpdate, SocketAuctionEnded, BackendAuctionData, ServerBid } from "@/lib/types";
import { auctionService } from "@/lib/auctionService";
import { useUsername } from "@/lib/contexts/UsernameContext";
import { useSocketContext } from "@/lib/contexts/SocketContext";
import { UsernameDialog } from "@/components/UsernameDialog";
import { WinnerModal } from "@/components/WinnerModal";
import useSocket from "@/hooks/useSocket";
import { timeSyncService } from "@/lib/timeSync";
import {
  ArrowLeft,
  Share2,
  Heart,
  ShieldCheck,
  Award,
  Eye,
  Clock,
  Users,
  Wifi,
  WifiOff,
  Play,
  Pause,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";

const ReelDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { username, hasUsername, flightNumber } = useUsername();

  // Socket.IO integration with error handling
  const [socketFunctions, setSocketFunctions] = useState<{
    isConnected: boolean;
    subscribeToAuction?: (id: string) => void;
    unsubscribeFromAuction?: (id: string) => void;
    placeBid?: (data: any) => void;
    onNewBid?: (callback: any) => () => void;
    onAuctionUpdate?: (callback: any) => () => void;
    onBidError?: (callback: any) => () => void;
    onAuctionEnded?: (callback: any) => () => void;
    onExistingBids?: (callback: any) => () => void;
    getServerTime?: () => number;
    isTimeSynchronized?: () => boolean;
  }>({
    isConnected: false
  });

  useEffect(() => {
    try {
      const socketContext = useSocketContext();
      const socketHook = useSocket();

      setSocketFunctions({
        isConnected: socketContext.isConnected,
        subscribeToAuction: socketHook.subscribeToAuction,
        unsubscribeFromAuction: socketHook.unsubscribeFromAuction,
        placeBid: socketHook.placeBid,
        onNewBid: socketHook.onNewBid,
        onAuctionUpdate: socketHook.onAuctionUpdate,
        onBidError: socketHook.onBidError,
        onAuctionEnded: socketHook.onAuctionEnded,
        onExistingBids: socketHook.onExistingBids,
        getServerTime: socketHook.getServerTime,
        isTimeSynchronized: socketHook.isTimeSynchronized,
      });
    } catch (error) {
      console.error('Socket.IO not available:', error);
      // Socket.IO is not available, continue without it
      setSocketFunctions({ isConnected: false });
    }
  }, []);
  const [reel, setReel] = useState<LiveReel | null>(null);
  const [bids, setBids] = useState<Bid[]>([]);
  const [isLiked, setIsLiked] = useState(false);
  const [showUsernameDialog, setShowUsernameDialog] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [isPlaying, setIsPlaying] = useState(false);
  const [backendEndTime, setBackendEndTime] = useState<number | null>(null); // Store endTime from backend
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const [auctionEndedData, setAuctionEndedData] = useState<SocketAuctionEnded | null>(null);
  const [auctionStatus, setAuctionStatus] = useState<'active' | 'completed'>('active');
  const [auctionWinner, setAuctionWinner] = useState<Bid | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Initialize reel data and Socket.IO subscription
  useEffect(() => {
    if (id) {
      const foundReel = liveReels.find((r) => r.id === id);
      if (foundReel) {
        setReel(foundReel);
        const history = bidHistories.find((h) => h.reelId === id);
        setBids(history?.bids || []);

        // Fetch endTime, status, and bids from backend
        auctionService.getAuction(id).then((backendData) => {
          if (backendData) {
            if (backendData.endTime) {
              setBackendEndTime(backendData.endTime);
              console.log(`Fetched endTime from backend for auction ${id}: ${new Date(backendData.endTime)}`);
            }

            // Load existing bids from server
            if (backendData.bids && backendData.bids.length > 0) {
              console.log(`Loading ${backendData.bids.length} existing bids from server for auction ${id}`);

              // Transform server bids to frontend format
              const transformedBids: Bid[] = backendData.bids.map(serverBid => ({
                id: serverBid.id,
                reelId: serverBid.auctionId, // Transform auctionId to reelId
                amount: serverBid.amount,
                bidder: serverBid.bidder,
                timestamp: new Date(serverBid.timestamp), // Transform string to Date
                serverTime: serverBid.serverTime
              }));

              console.log(`Transformed ${transformedBids.length} server bids to frontend format`);
              setBids(transformedBids);

              // Update reel with current bid and bid count from server
              setReel(prevReel => {
                if (!prevReel) return prevReel;
                return {
                  ...prevReel,
                  product: {
                    ...prevReel.product,
                    currentBid: backendData.currentBid,
                  },
                  bidCount: backendData.bidCount,
                };
              });
            }

            // Check if auction is already completed
            if (backendData.status === 'completed') {
              setAuctionStatus('completed');
              if (backendData.winner) {
                // Transform server winner bid to frontend format
                const transformedWinner: Bid = {
                  id: backendData.winner.id,
                  reelId: backendData.winner.auctionId,
                  amount: backendData.winner.amount,
                  bidder: backendData.winner.bidder,
                  timestamp: new Date(backendData.winner.timestamp),
                  serverTime: backendData.winner.serverTime
                };
                setAuctionWinner(transformedWinner);
              }
              console.log(`Auction ${id} is already completed. Winner: ${backendData.winner ? backendData.winner.bidder.name : 'No bids'}`);
            }
          } else {
            console.warn(`No auction data found in backend for auction ${id}, using frontend fallback`);
          }
        }).catch((error) => {
          console.error(`Failed to fetch auction data for ${id}:`, error);
        });

        // Subscribe to auction updates via Socket.IO if available and connected
        if (socketFunctions.subscribeToAuction && socketFunctions.isConnected) {
          console.log(`Subscribing to auction ${id} via Socket.IO`);
          socketFunctions.subscribeToAuction(id);
        } else {
          console.log(`Socket.IO not ready for subscription. Connected: ${socketFunctions.isConnected}, Function available: ${!!socketFunctions.subscribeToAuction}`);
        }
      }
    }

    // Cleanup: unsubscribe when component unmounts or id changes
    return () => {
      if (id && socketFunctions.unsubscribeFromAuction) {
        socketFunctions.unsubscribeFromAuction(id);
      }
    };
  }, [id]);

  // Separate useEffect for Socket.IO subscription when connection becomes ready
  useEffect(() => {
    if (id && socketFunctions.subscribeToAuction && socketFunctions.isConnected) {
      console.log(`Socket.IO connection ready - subscribing to auction ${id}`);
      socketFunctions.subscribeToAuction(id);

      // Cleanup: unsubscribe when connection changes
      return () => {
        if (socketFunctions.unsubscribeFromAuction) {
          console.log(`Unsubscribing from auction ${id} due to connection change`);
          socketFunctions.unsubscribeFromAuction(id);
        }
      };
    }
  }, [id, socketFunctions.subscribeToAuction, socketFunctions.unsubscribeFromAuction, socketFunctions.isConnected]);

  // Auto-play video when reel loads
  useEffect(() => {
    if (reel && videoRef.current) {
      videoRef.current
        .play()
        .then(() => {
          setIsPlaying(true);
        })
        .catch((error) => {
          console.log("Auto-play failed:", error);
          // Auto-play failed, user interaction required
        });
    }
  }, [reel]);

  // Listen for real-time bid updates
  useEffect(() => {
    if (!socketFunctions.onNewBid) return; // Skip if Socket.IO is not available

    const cleanup = socketFunctions.onNewBid((data: SocketNewBidData) => {
      const newBid: Bid = {
        id: data.bid.id,
        reelId: data.bid.auctionId,
        amount: data.bid.amount,
        bidder: data.bid.bidder,
        timestamp: new Date(data.bid.timestamp),
      };

      // Update bids list
      setBids(prevBids => [newBid, ...prevBids]);

      // Update current bid in reel
      setReel(prevReel => {
        if (!prevReel) return prevReel;
        return {
          ...prevReel,
          product: {
            ...prevReel.product,
            currentBid: data.bid.amount,
          },
          bidCount: prevReel.bidCount + 1,
        };
      });

      // Show toast notification for new bids from other users
      if (data.bid.bidder.name !== username) {
        toast({
          title: "New Bid!",
          description: `${data.bid.bidder.name} bid $${data.bid.amount.toLocaleString()}`,
        });
      }
    });

    return cleanup;
  }, [socketFunctions.onNewBid, username]);

  // Listen for auction updates (including endTime)
  useEffect(() => {
    if (!socketFunctions.onAuctionUpdate) return;

    const cleanup = socketFunctions.onAuctionUpdate((data: SocketAuctionUpdate) => {
      console.log('Received auction update:', data);

      // Update endTime if provided from backend
      if (data.endTime) {
        setBackendEndTime(data.endTime);
        console.log(`Updated endTime from Socket.IO for auction ${data.auctionId}: ${new Date(data.endTime)}`);
      }

      // Update reel data
      setReel(prevReel => {
        if (!prevReel || prevReel.id !== data.auctionId) return prevReel;
        return {
          ...prevReel,
          product: {
            ...prevReel.product,
            currentBid: data.currentBid,
          },
          bidCount: data.bidCount,
        };
      });
    });

    return cleanup;
  }, [socketFunctions.onAuctionUpdate]);

  // Listen for bid errors
  useEffect(() => {
    if (!socketFunctions.onBidError) return;

    const cleanup = socketFunctions.onBidError((data: { message: string }) => {
      toast({
        title: "Bid Error",
        description: data.message,
        variant: "destructive",
      });
    });

    return cleanup;
  }, [socketFunctions.onBidError]);

  // Listen for auction ended events
  useEffect(() => {
    if (!socketFunctions.onAuctionEnded) return;

    const cleanup = socketFunctions.onAuctionEnded((data: SocketAuctionEnded) => {
      console.log('Auction ended:', data);

      // Only process if this is for the current auction
      if (data.auctionId === id) {
        // Update auction status and winner
        setAuctionStatus('completed');
        setAuctionWinner(data.winner);

        // Store the auction ended data
        setAuctionEndedData(data);

        // Show the winner modal
        setShowWinnerModal(true);

        // Show toast notification
        if (data.winner && data.winner.bidder.name === username) {
          toast({
            title: "🎉 Congratulations!",
            description: "You won the auction!",
            duration: 5000,
          });
        } else {
          toast({
            title: "Auction Ended",
            description: data.winner
              ? `Won by ${data.winner.bidder.name}`
              : "No bids were placed",
            duration: 5000,
          });
        }
      }
    });

    return cleanup;
  }, [socketFunctions.onAuctionEnded, username, id]);

  // Listen for existing bids when subscribing to an auction
  useEffect(() => {
    if (!socketFunctions.onExistingBids) return;

    const cleanup = socketFunctions.onExistingBids((data: { auctionId: string; bids: any[]; serverTime: number }) => {
      // Only process if this is for the current auction
      if (data.auctionId === id && data.bids && data.bids.length > 0) {
        console.log(`Loading ${data.bids.length} existing bids from Socket.IO for auction ${id}`);

        // Convert server bids to frontend format
        const formattedBids: Bid[] = data.bids.map(bid => ({
          id: bid.id,
          reelId: bid.auctionId, // Transform auctionId to reelId
          amount: bid.amount,
          bidder: bid.bidder,
          timestamp: new Date(bid.timestamp), // Transform string to Date
          serverTime: bid.serverTime
        }));

        // Update bids list (merge with existing bids, avoiding duplicates)
        setBids(prevBids => {
          const existingBidIds = new Set(prevBids.map(bid => bid.id));
          const newBids = formattedBids.filter(bid => !existingBidIds.has(bid.id));
          return [...newBids, ...prevBids];
        });
      }
    });

    return cleanup;
  }, [socketFunctions.onExistingBids, id]);

  // Update timer every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handlePlaceBid = useCallback((amount: number) => {
    if (!reel) return;

    // Check if username is set before allowing bid
    if (!hasUsername) {
      setShowUsernameDialog(true);
      return;
    }

    // Use Socket.IO if available, otherwise fall back to local state
    if (socketFunctions.placeBid && socketFunctions.isConnected) {
      // Place bid via Socket.IO
      socketFunctions.placeBid({
        auctionId: reel.id,
        username: username,
        price: amount,
        ...(flightNumber && { flightNumber }),
      });

      toast({
        title: "Bid Submitted!",
        description: `Your bid of $${amount.toLocaleString()} is being processed.`,
      });
    } else {
      // Fallback to local state update
      const newBid: Bid = {
        id: `bid-${Date.now()}`,
        reelId: reel.id,
        amount,
        bidder: {
          name: username,
          avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face",
          ...(flightNumber && { flightNumber }),
        },
        timestamp: new Date(),
      };

      setBids([newBid, ...bids]);
      setReel({
        ...reel,
        product: {
          ...reel.product,
          currentBid: amount,
        },
        bidCount: reel.bidCount + 1,
      });

      toast({
        title: "Bid Placed!",
        description: `Your bid of $${amount.toLocaleString()} has been placed.`,
      });
    }
  }, [reel, hasUsername, username, bids, socketFunctions.placeBid, socketFunctions.isConnected]);

  const handleVideoToggle = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch((error) => {
            console.log("Play failed:", error);
          });
      }
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: reel?.title,
        text: `Check out this live auction: ${reel?.product.name}`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link Copied",
        description: "Auction link copied to clipboard",
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!reel) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Auction Not Found
          </h2>
          <p className="text-gray-600 mb-6">
            The auction you're looking for doesn't exist or has ended.
          </p>
          <Button onClick={() => navigate("/")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Auctions
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => navigate("/")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Auctions
            </Button>

            <div className="flex items-center gap-2">
              {/* Connection Status Indicator */}
              <div className={cn(
                "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                socketFunctions.isConnected
                  ? "bg-green-100 text-green-700"
                  : "bg-red-100 text-red-700"
              )}>
                {socketFunctions.isConnected ? (
                  <>
                    <Wifi className="w-3 h-3" />
                    Live
                  </>
                ) : (
                  <>
                    <WifiOff className="w-3 h-3" />
                    Offline
                  </>
                )}
              </div>

              {hasUsername && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUsernameDialog(true)}
                  className="flex items-center gap-1 text-blue-600"
                >
                  <Users className="w-4 h-4" />
                  {username}
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={cn(
                  "flex items-center gap-1",
                  isLiked && "text-red-500",
                )}
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
                {isLiked ? "Liked" : "Like"}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="flex items-center gap-1"
              >
                <Share2 className="w-4 h-4" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Video */}
            <Card className="overflow-hidden">
              <div className="relative aspect-video">
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                  poster={reel.product.imageUrl}
                  loop
                  playsInline
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onClick={handleVideoToggle}
                >
                  <source
                    src={reel.videoUrl}
                    type="video/mp4"
                  />
                  {/* Fallback image if video fails to load */}
                  <img
                    src={reel.product.imageUrl}
                    alt={reel.product.name}
                    className="w-full h-full object-cover"
                  />
                </video>

                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent pointer-events-none" />

                {/* Live indicator */}
                {reel.isLive && (
                  <div className="absolute top-4 left-4 flex items-center gap-2 bg-red-500 text-white px-3 py-1.5 rounded-full text-sm font-medium">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                    LIVE AUCTION
                  </div>
                )}

                {/* Viewer count */}
                <div className="absolute top-4 right-4 flex items-center gap-1 bg-black/50 text-white px-3 py-1.5 rounded-full text-sm">
                  <Eye className="w-4 h-4" />
                  {reel.viewerCount.toLocaleString()}
                </div>

                {/* Play/Pause button overlay */}
                <div className="absolute bottom-4 right-4">
                  <Button
                    size="sm"
                    onClick={handleVideoToggle}
                    className="bg-black/60 backdrop-blur-sm border-white/30 text-white hover:bg-black/80 rounded-full"
                    variant="outline"
                  >
                    {isPlaying ? (
                      <Pause className="w-4 h-4" />
                    ) : (
                      <Play className="w-4 h-4 ml-0.5" />
                    )}
                  </Button>
                </div>

                {/* Video duration indicator */}
                <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium">
                  0:
                  {Math.floor(Math.random() * 60)
                    .toString()
                    .padStart(2, "0")}
                </div>
              </div>
            </Card>

            {/* Product Details */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-2xl mb-2">
                      {reel.product.name}
                    </CardTitle>
                    <p className="text-gray-600 text-lg">
                      {reel.product.description}
                    </p>
                  </div>
                  <Badge variant="secondary" className="ml-4">
                    {reel.product.category}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Condition</div>
                    <div className="font-medium">{reel.product.condition}</div>
                  </div>
                  {reel.product.brand && (
                    <div>
                      <div className="text-sm text-gray-500">Brand</div>
                      <div className="font-medium">{reel.product.brand}</div>
                    </div>
                  )}
                  <div>
                    <div className="text-sm text-gray-500">Starting Price</div>
                    <div className="font-medium">
                      {formatCurrency(reel.product.startingPrice)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Total Bids</div>
                    <div className="font-medium">{reel.bidCount}</div>
                  </div>
                </div>

                <Separator />
              </CardContent>
            </Card>

            {/* Auction Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Auction Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">
                      {reel.viewerCount.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Viewers</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <Award className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">
                      {reel.bidCount}
                    </div>
                    <div className="text-sm text-gray-600">Total Bids</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <Clock className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-purple-600">
                      {(() => {
                        // Use synchronized time if available, otherwise fall back to local time
                        const now = socketFunctions.getServerTime ? socketFunctions.getServerTime() : currentTime;

                        // Use backend endTime if available, otherwise fall back to frontend endTime
                        const endTime = backendEndTime ? backendEndTime : reel.endTime.getTime();
                        const timeLeft = Math.floor((endTime - now) / (1000 * 60));
                        return Math.max(0, timeLeft);
                      })()}
                      m
                    </div>
                    <div className="text-sm text-gray-600">
                      Time Left {backendEndTime ? '(Backend)' : '(Frontend)'} {socketFunctions.isTimeSynchronized && socketFunctions.isTimeSynchronized() ? '(Synced)' : '(Local)'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Bidding Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <BiddingPanel
                reel={reel}
                bids={bids}
                onPlaceBid={handlePlaceBid}
                backendEndTime={backendEndTime}
                auctionStatus={auctionStatus}
                winner={auctionWinner}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Username Dialog */}
      <UsernameDialog
        open={showUsernameDialog}
        onOpenChange={setShowUsernameDialog}
        required={!hasUsername}
      />

      {/* Winner Modal */}
      {auctionEndedData && (
        <WinnerModal
          isOpen={showWinnerModal}
          onClose={() => setShowWinnerModal(false)}
          auctionData={auctionEndedData}
          isWinner={auctionEndedData.winner?.bidder.name === username}
          currentUsername={username}
        />
      )}
    </div>
  );
};

export default ReelDetail;
